<?php

namespace App\Console\Commands;

use App\Jobs\Subscription\ResumeJob;
use App\Subscription;
use Illuminate\Console\Command;

class RecurringOrders extends Command
{
    protected $signature = 'recurring:orders {--again=0}';
    protected $description = 'Command description';

    public function handle(): void
    {
        $subscriptions = Subscription::query()
            ->select('id')
            ->active()
            ->where('issues', 0)
            ->where('renew', true)
            ->where('payment_type', 'Credit Card')
            ->pluck('id');

        $subscriptions->each(function ($subscription, $index) {
            ResumeJob::dispatch($subscription, failedRenew: (bool)$this->option('again'))->delay(now()->addMinutes($index * 2));
        });
    }
}
