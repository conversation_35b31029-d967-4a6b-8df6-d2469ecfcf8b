<?php

namespace App\Lunar;

use Lunar\Models\ProductVariant;
use Lunar\Models\Product;

class Discount extends \Lunar\Models\Discount
{
    protected string $morphClass = \Lunar\Models\Discount::class;

    public function getExclusionamountoffAttribute(): bool
    {
        return $this->purchasableExclusions->count() > 0;
    }

    public function getLimitationsAttribute()
    {
        //nova attribute
        return $this->purchasableLimitations->filter(function ($item) {
            return $item->purchasable_type == Product::class;
        })->pluck('purchasable_id')->toArray();
    }

    public function getExclusionsAttribute()
    {
        //nova attribute
        return $this->purchasableExclusions->filter(function ($item) {
            return $item->purchasable_type == Product::class;
        })->pluck('purchasable_id')->toArray();
    }
    public function getRewardsAttribute()
    {
        //nova attribute
        return $this->purchasableRewards->filter(function ($item) {
            return $item->purchasable_type == Product::class;
        })->pluck('purchasable_id')->toArray();
    }
    public function getConditionsAttribute()
    {
        //nova attribute
        return $this->purchasableConditions->filter(function ($item) {
            return $item->purchasable_type == Product::class;
        })->pluck('purchasable_id')->toArray();
    }

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'data' => 'array',
        'enabled' => 'boolean',
    ];


    public function purchasableProducts(): \Illuminate\Database\Eloquent\Relations\MorphToMany
    {
        return $this->morphedByMany(
            Product::class,
            'purchasable',
            'lunar_discount_purchasables',
        )
            ->withPivot(['type']);
    }

    public function purchasableProductVariants(): \Illuminate\Database\Eloquent\Relations\MorphToMany
    {
        return $this->morphedByMany(
            ProductVariant::class,
            'purchasable',
            'lunar_discount_purchasables',
        )
            ->withPivot(['type']);
    }
    public static function boot(): void
    {
        parent::boot();

        static::created(function ($discount) {
            //Attach Channles
            $channelData = \Lunar\Models\Channel::all()->mapWithKeys(function ($channel) {
                return [
                    $channel->id => [
                        'enabled' => true,
                        'starts_at' => null,
                        'ends_at' => null,
                    ]
                ];
            });

            $discount->channels()->sync($channelData);

            // attach customers
            $customerGroupData = \Lunar\Models\CustomerGroup::all()->mapWithKeys(function ($channel) {
                return [
                    $channel->id => [
                        'enabled' => true,
                        'starts_at' => null,
                        'ends_at' => null,
                    ]
                ];
            });
            $discount->customerGroups()->sync($customerGroupData);
        });
    }
}
