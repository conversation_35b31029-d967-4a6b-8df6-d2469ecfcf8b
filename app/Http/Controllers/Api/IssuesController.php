<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Product;
use App\SubscriptionMonth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class IssuesController extends Controller
{
    public function index(Request $request)
    {
        return Product::issues()
            ->with(['variants', 'prices.currency', 'prices.priceable', 'media'])
            ->status('published')
            ->when($request->has('years'), function ($query) use ($request) {
                $query->where(function ($query) use ($request) {
                    foreach ($request->input('years', []) as $year) {
                        $query->orWhere('attribute_data->name->value', 'Like', '%' . $year . '%');
                    }
                });
            })

            ->latest('id')
            ->paginate(36);
    }

    public static function latest()
    {
        return Product::issues()
            ->status('published')
            ->latest('id')
            ->take(4)
            ->get();
    }

    public function filter()
    {
        $entries = DB::table('lunar_products')
            ->select('attribute_data')
            ->where('product_type_id', Product::$productTypes['issues'])
            ->where('status', 'published')
            ->get();

        $years = $entries->map(function ($entry) {
            $data = json_decode($entry->attribute_data, true);
            if (isset($data['name']['value'])) {
                preg_match('/תשפ["\'׳״]*\w*/u', $data['name']['value'], $matches);
                return $matches[0] ?? null;
            }
            return null;
        })->filter()->unique()->map(function ($year) {
            $cleanYear = preg_replace('/["\'׳״]/u', '', $year);
            $numericYear = 5000 + hebrewToNumber($cleanYear);
            return ['original' => $year, 'numeric' => $numericYear];
        })->filter(function($item){
            $current = currentHebRoshChodesh();
            return $item['numeric'] <= $current['year'];
        })->sortByDesc('numeric')->pluck('original')->values();

        return response()->json([
            'years' => $years
        ]);
    }
}
